# Pressure Washing Landing Page

A high-converting landing page for a pressure washing business built with Next.js, TypeScript, and Tailwind CSS.

## Features

- **High-Converting Design**: Optimized for conversions with urgency indicators, social proof, and multiple CTAs
- **$399 Soft Wash Package**: Limited-time offer for the next 8 customers
- **Cal.com Integration**: Instant appointment booking system
- **Mobile Responsive**: Fully optimized for all device sizes
- **Performance Optimized**: Fast loading times and SEO-friendly

## Key Sections

1. **Hero Section**: Compelling headline with value proposition and primary CTA
2. **Offer Details**: Complete breakdown of the $399 package with value comparison
3. **Social Proof**: Customer testimonials and trust indicators
4. **Booking Section**: Cal.com integration for instant scheduling
5. **FAQ Section**: Addresses common customer concerns
6. **Final CTA**: Urgency-driven conversion section with countdown timer

## Setup Instructions

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Configure Cal.com**:
   - Replace `your-username` in the Cal.com iframe URL with your actual Cal.com username
   - Update the booking URL in `src/app/page.tsx` line 407

3. **Update Contact Information**:
   - Replace phone number `(*************` with your actual number
   - Update service area in "Serving Greater Metro Area"
   - Add your actual business name and details

4. **Customize Content**:
   - Update testimonials with real customer reviews
   - Add actual before/after photos
   - Modify service descriptions to match your offerings

5. **Run Development Server**:
   ```bash
   npm run dev
   ```

6. **Build for Production**:
   ```bash
   npm run build
   npm start
   ```

## Conversion Optimization Features

- **Urgency Indicators**: Countdown timer and limited spots available
- **Social Proof**: Customer testimonials and ratings
- **Risk Reversal**: 100% satisfaction guarantee
- **Multiple CTAs**: Strategic placement throughout the page
- **Mobile Optimization**: Responsive design for all devices
- **Fast Loading**: Optimized for performance and SEO

## Cal.com Setup

1. Create a Cal.com account at https://cal.com
2. Set up your booking page for pressure washing consultations
3. Replace the iframe URL in the booking section with your Cal.com embed link
4. Test the booking flow to ensure it works correctly

## Customization Tips

- Update the countdown timer duration in the `timeLeft` state
- Modify the number of available spots in the `spotsLeft` state
- Customize colors and styling in `tailwind.config.js`
- Add Google Analytics or other tracking scripts in `layout.tsx`

## Performance Notes

- Images should be optimized and served from a CDN
- Consider adding actual before/after photos for better conversion
- Test loading speed and optimize as needed
- Ensure all forms and CTAs are working properly

## License

This project is for commercial use by the pressure washing business.
