import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Professional Pressure Washing - $399 Full Exterior Soft Wash Package',
  description: 'Transform your home with our professional soft wash service. Limited time offer: Complete exterior cleaning for just $399. Only 8 spots available!',
  keywords: 'pressure washing, soft wash, exterior cleaning, house washing, professional cleaning',
  openGraph: {
    title: 'Professional Pressure Washing - $399 Full Exterior Soft Wash Package',
    description: 'Transform your home with our professional soft wash service. Limited time offer: Complete exterior cleaning for just $399. Only 8 spots available!',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function (C, A, L) { let p = function (a, ar) { a.q.push(ar); }; let d = C.document; C.Cal = C.Cal || function () { let cal = C.Cal; let ar = arguments; if (!cal.q) { cal.q = []; } p(cal, ar); }; C.Cal.ns = {}; C.Cal.q = C.Cal.q || []; d.head.appendChild(d.createElement("script")).src = A; })(window, "https://app.cal.com/embed/embed.js", "");
            `
          }}
        />
      </head>
      <body className={inter.className}>{children}</body>
    </html>
  )
}