'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, Star, Clock, Shield, Phone, MapPin, Droplets, Sparkles, Users, Calendar } from 'lucide-react'

export default function Home() {
  const [spotsLeft, setSpotsLeft] = useState(8)
  const [timeLeft, setTimeLeft] = useState({
    hours: 23,
    minutes: 45,
    seconds: 30
  })

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { hours: prev.hours - 1, minutes: 59, seconds: 59 }
        }
        return prev
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const scrollToBooking = () => {
    document.getElementById('booking')?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Headlines & CTA */}
            <div className="animate-fadeInUp">
              {/* Urgency Banner */}
              <div className="inline-flex items-center bg-red-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6 pulse-glow">
                <Clock className="w-4 h-4 mr-2" />
                Only {spotsLeft} spots left! Offer expires in {timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
              </div>
              
              <h1 className="text-4xl lg:text-6xl font-bold leading-tight mb-6">
                Transform Your Home's
                <span className="block text-yellow-300">Exterior in One Day</span>
              </h1>
              
              <p className="text-xl lg:text-2xl mb-8 text-blue-100">
                Professional soft wash service that safely removes dirt, mold, and stains without damaging your property
              </p>
              
              {/* Value Proposition */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
                <div className="flex items-center mb-4">
                  <Sparkles className="w-6 h-6 text-yellow-300 mr-3" />
                  <h3 className="text-2xl font-bold">Complete Exterior Package</h3>
                </div>
                <div className="grid sm:grid-cols-2 gap-3 text-lg">
                  <div className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-300 mr-2" />
                    House Siding & Trim
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-300 mr-2" />
                    Roof Cleaning
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-300 mr-2" />
                    Driveway & Walkways
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-300 mr-2" />
                    Deck/Patio Cleaning
                  </div>
                </div>
              </div>
              
              {/* Price & CTA */}
              <div className="text-center lg:text-left">
                <div className="mb-6">
                  <span className="text-3xl text-blue-200 line-through">$699</span>
                  <span className="text-5xl lg:text-6xl font-bold text-yellow-300 ml-4">$399</span>
                  <span className="text-xl text-blue-100 block">Limited Time Offer</span>
                </div>
                
                <button 
                  onClick={scrollToBooking}
                  className="bg-yellow-400 hover:bg-yellow-300 text-blue-900 font-bold text-xl px-8 py-4 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-200 mb-4 w-full lg:w-auto"
                >
                  Book Your Instant Appointment
                </button>
                
                <p className="text-blue-100 text-sm">
                  <Shield className="w-4 h-4 inline mr-1" />
                  100% Satisfaction Guarantee • Fully Insured • Free Estimates
                </p>
              </div>
            </div>
            
            {/* Right Column - Before/After or Hero Image */}
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center">
                <div className="bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg p-12 mb-6">
                  <Droplets className="w-24 h-24 text-white mx-auto mb-4" />
                  <h3 className="text-2xl font-bold mb-2">Professional Equipment</h3>
                  <p className="text-blue-100">Eco-friendly solutions that protect your landscaping</p>
                </div>
                
                {/* Trust Indicators */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold text-yellow-300">500+</div>
                    <div className="text-sm text-blue-100">Happy Customers</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-yellow-300">5★</div>
                    <div className="text-sm text-blue-100">Average Rating</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-yellow-300">10+</div>
                    <div className="text-sm text-blue-100">Years Experience</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Offer Details Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              What's Included in Your $399 Package
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive soft wash service covers every exterior surface of your home,
              using eco-friendly solutions that are safe for your family, pets, and landscaping.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            {/* Service Details */}
            <div>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="bg-blue-100 rounded-lg p-3 mr-4">
                    <Droplets className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">House Siding & Trim</h3>
                    <p className="text-gray-600">Complete cleaning of all exterior siding, removing dirt, mold, mildew, and organic stains. Includes all trim work and detail areas.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-blue-100 rounded-lg p-3 mr-4">
                    <Shield className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Roof Soft Wash</h3>
                    <p className="text-gray-600">Gentle roof cleaning that removes algae, moss, and black streaks without damaging shingles. Extends roof life significantly.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-blue-100 rounded-lg p-3 mr-4">
                    <Sparkles className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Driveway & Walkways</h3>
                    <p className="text-gray-600">Deep cleaning of concrete surfaces, removing oil stains, dirt buildup, and organic growth for a like-new appearance.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-blue-100 rounded-lg p-3 mr-4">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Deck/Patio Cleaning</h3>
                    <p className="text-gray-600">Thorough cleaning of outdoor living spaces, removing grime and preparing surfaces for staining or sealing if desired.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Value Comparison */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why This Deal Won't Last</h3>

              <div className="space-y-4 mb-8">
                <div className="flex justify-between items-center py-2 border-b border-blue-200">
                  <span className="text-gray-700">House Siding Cleaning</span>
                  <span className="font-semibold text-gray-900">$299</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-blue-200">
                  <span className="text-gray-700">Roof Soft Wash</span>
                  <span className="font-semibold text-gray-900">$249</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-blue-200">
                  <span className="text-gray-700">Driveway Cleaning</span>
                  <span className="font-semibold text-gray-900">$99</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-blue-200">
                  <span className="text-gray-700">Deck/Patio Cleaning</span>
                  <span className="font-semibold text-gray-900">$149</span>
                </div>
                <div className="flex justify-between items-center py-3 bg-red-100 px-4 rounded-lg">
                  <span className="font-semibold text-gray-900">Regular Total:</span>
                  <span className="font-bold text-red-600 text-xl line-through">$796</span>
                </div>
                <div className="flex justify-between items-center py-3 bg-green-100 px-4 rounded-lg">
                  <span className="font-semibold text-gray-900">Your Price Today:</span>
                  <span className="font-bold text-green-600 text-2xl">$399</span>
                </div>
              </div>

              <div className="text-center">
                <div className="bg-red-500 text-white px-4 py-2 rounded-full inline-flex items-center mb-4">
                  <Clock className="w-4 h-4 mr-2" />
                  Only {spotsLeft} spots remaining
                </div>
                <p className="text-sm text-gray-600">
                  This promotional pricing is only available to the next 8 customers who book today.
                </p>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <button
              onClick={scrollToBooking}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold text-xl px-12 py-4 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-200"
            >
              Secure Your $399 Package Now
            </button>
            <p className="text-gray-500 text-sm mt-4">
              Book now, pay after completion • No upfront payment required
            </p>
          </div>
        </div>
      </section>

      {/* Social Proof & Testimonials */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              What Our Customers Say
            </h2>
            <div className="flex justify-center items-center space-x-2 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-8 h-8 text-yellow-400 fill-current" />
              ))}
              <span className="text-2xl font-bold text-gray-900 ml-4">4.9/5</span>
            </div>
            <p className="text-xl text-gray-600">Based on 500+ verified customer reviews</p>
          </div>

          {/* Testimonials Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 mb-4">
                "Absolutely amazing results! My house looks brand new. The team was professional,
                punctual, and the price was incredibly fair. I've already recommended them to my neighbors."
              </p>
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mr-3">
                  <span className="text-blue-600 font-semibold">SM</span>
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Sarah Mitchell</div>
                  <div className="text-gray-500 text-sm">Homeowner, Oak Ridge</div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 mb-4">
                "I was skeptical about the soft wash process, but wow! They removed years of algae
                from my roof without any damage. My home's curb appeal has increased dramatically."
              </p>
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mr-3">
                  <span className="text-blue-600 font-semibold">MJ</span>
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Mike Johnson</div>
                  <div className="text-gray-500 text-sm">Homeowner, Riverside</div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 mb-4">
                "Best investment I've made for my home! The before and after difference is incredible.
                Professional service from start to finish. Will definitely use them again."
              </p>
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mr-3">
                  <span className="text-blue-600 font-semibold">LR</span>
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Lisa Rodriguez</div>
                  <div className="text-gray-500 text-sm">Homeowner, Westfield</div>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Badges */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">Why Choose Us</h3>
            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Fully Insured</h4>
                <p className="text-gray-600 text-sm">$2M liability coverage for your peace of mind</p>
              </div>

              <div>
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Satisfaction Guarantee</h4>
                <p className="text-gray-600 text-sm">100% money-back guarantee if not completely satisfied</p>
              </div>

              <div>
                <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Experienced Team</h4>
                <p className="text-gray-600 text-sm">10+ years of professional exterior cleaning experience</p>
              </div>

              <div>
                <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-8 h-8 text-yellow-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Eco-Friendly</h4>
                <p className="text-gray-600 text-sm">Safe for your family, pets, and landscaping</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Section */}
      <section id="booking" className="py-20 bg-gradient-to-br from-blue-600 to-blue-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Book Your $399 Soft Wash Package
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Instant scheduling • No upfront payment • 100% satisfaction guarantee
            </p>

            {/* Urgency Reminder */}
            <div className="bg-red-500 text-white px-6 py-3 rounded-full inline-flex items-center mb-8 pulse-glow">
              <Clock className="w-5 h-5 mr-2" />
              Hurry! Only {spotsLeft} spots left at this price
            </div>
          </div>

          {/* Cal.com Booking Widget */}
          <div className="bg-white rounded-lg shadow-2xl p-8">
            <div className="text-center mb-6">
              <Calendar className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Schedule Your Appointment</h3>
              <p className="text-gray-600">Choose your preferred date and time below</p>
            </div>

            {/* Cal.com Embed - Replace 'your-username' with actual Cal.com username */}
            <div className="cal-embed-container">
              <iframe
                src="https://cal.com/your-username/pressure-washing-consultation?embed=true"
                width="100%"
                height="600"
                frameBorder="0"
                className="rounded-lg"
                title="Book Pressure Washing Appointment"
              ></iframe>
            </div>

            {/* Fallback Contact Info */}
            <div className="mt-8 p-6 bg-gray-50 rounded-lg">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 text-center">
                Prefer to call? We're here to help!
              </h4>
              <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-8">
                <a
                  href="tel:+1234567890"
                  className="flex items-center text-blue-600 hover:text-blue-700 font-semibold"
                >
                  <Phone className="w-5 h-5 mr-2" />
                  (*************
                </a>
                <div className="flex items-center text-gray-600">
                  <MapPin className="w-5 h-5 mr-2" />
                  Serving Greater Metro Area
                </div>
              </div>
              <p className="text-center text-gray-500 text-sm mt-4">
                Call now for immediate scheduling or questions about our services
              </p>
            </div>
          </div>

          {/* Final Value Props */}
          <div className="grid md:grid-cols-3 gap-6 mt-12 text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <CheckCircle className="w-8 h-8 text-green-300 mx-auto mb-3" />
              <h4 className="font-semibold mb-2">No Upfront Payment</h4>
              <p className="text-blue-100 text-sm">Pay only after you're 100% satisfied with the results</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <Shield className="w-8 h-8 text-green-300 mx-auto mb-3" />
              <h4 className="font-semibold mb-2">Fully Insured</h4>
              <p className="text-blue-100 text-sm">$2M liability coverage protects your property</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <Sparkles className="w-8 h-8 text-green-300 mx-auto mb-3" />
              <h4 className="font-semibold mb-2">Same Day Service</h4>
              <p className="text-blue-100 text-sm">Most appointments available within 24-48 hours</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need to know about our soft wash service
            </p>
          </div>

          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                What is soft washing and how is it different from pressure washing?
              </h3>
              <p className="text-gray-700">
                Soft washing uses low-pressure water combined with specialized cleaning solutions to safely remove dirt,
                mold, algae, and stains. Unlike high-pressure washing, it won't damage delicate surfaces like siding,
                roofing, or landscaping while providing longer-lasting results.
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Is the cleaning solution safe for my family and pets?
              </h3>
              <p className="text-gray-700">
                Absolutely! We use eco-friendly, biodegradable cleaning solutions that are safe for your family,
                pets, and landscaping. All products are EPA-approved and specifically designed for exterior cleaning.
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                How long does the cleaning process take?
              </h3>
              <p className="text-gray-700">
                Most complete exterior packages take 4-6 hours depending on the size of your home. We work efficiently
                while ensuring thorough coverage of all surfaces included in your package.
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                What if I'm not satisfied with the results?
              </h3>
              <p className="text-gray-700">
                We offer a 100% satisfaction guarantee. If you're not completely happy with our work, we'll return
                to address any concerns at no additional cost, or provide a full refund.
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Do I need to be home during the cleaning?
              </h3>
              <p className="text-gray-700">
                No, you don't need to be present. We just need access to water and the exterior of your home.
                We'll provide before and after photos and a detailed completion report.
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Why is this offer only available to 8 customers?
              </h3>
              <p className="text-gray-700">
                This is a limited promotional offer to introduce our services to new customers in your area.
                We're offering this significant discount to build our local customer base and generate testimonials.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Don't Miss This Limited-Time Offer
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Transform your home's exterior for just $399 - but only for the next {spotsLeft} customers
          </p>

          {/* Countdown Timer */}
          <div className="bg-red-600 rounded-lg p-6 mb-8 inline-block">
            <div className="text-sm font-semibold mb-2">OFFER EXPIRES IN:</div>
            <div className="flex justify-center space-x-4 text-2xl font-bold">
              <div className="text-center">
                <div className="bg-white text-red-600 rounded px-3 py-2 min-w-[60px]">
                  {timeLeft.hours.toString().padStart(2, '0')}
                </div>
                <div className="text-xs mt-1">HOURS</div>
              </div>
              <div className="text-center">
                <div className="bg-white text-red-600 rounded px-3 py-2 min-w-[60px]">
                  {timeLeft.minutes.toString().padStart(2, '0')}
                </div>
                <div className="text-xs mt-1">MINUTES</div>
              </div>
              <div className="text-center">
                <div className="bg-white text-red-600 rounded px-3 py-2 min-w-[60px]">
                  {timeLeft.seconds.toString().padStart(2, '0')}
                </div>
                <div className="text-xs mt-1">SECONDS</div>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <button
              onClick={scrollToBooking}
              className="bg-yellow-400 hover:bg-yellow-300 text-black font-bold text-xl px-12 py-4 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-200 mb-4"
            >
              Book Your $399 Package Now
            </button>
            <p className="text-gray-300 text-sm">
              <Shield className="w-4 h-4 inline mr-1" />
              No upfront payment • 100% satisfaction guarantee • Fully insured
            </p>
          </div>

          {/* Final Trust Indicators */}
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-yellow-400 mb-2">500+</div>
              <div className="text-gray-300">Satisfied Customers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-yellow-400 mb-2">4.9★</div>
              <div className="text-gray-300">Average Rating</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-yellow-400 mb-2">$2M</div>
              <div className="text-gray-300">Insurance Coverage</div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">Professional Pressure Washing</h3>
              <p className="text-gray-400 mb-4">
                Transforming homes with safe, effective soft wash cleaning services.
              </p>
              <div className="flex space-x-4">
                <a href="tel:+1234567890" className="text-blue-400 hover:text-blue-300">
                  <Phone className="w-5 h-5" />
                </a>
                <a href="#" className="text-blue-400 hover:text-blue-300">
                  <MapPin className="w-5 h-5" />
                </a>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li>House Soft Washing</li>
                <li>Roof Cleaning</li>
                <li>Driveway Cleaning</li>
                <li>Deck & Patio Cleaning</li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <div className="space-y-2 text-gray-400">
                <div className="flex items-center">
                  <Phone className="w-4 h-4 mr-2" />
                  (*************
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2" />
                  Serving Greater Metro Area
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Professional Pressure Washing. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  )
}
